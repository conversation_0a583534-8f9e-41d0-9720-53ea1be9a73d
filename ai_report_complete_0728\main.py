#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI报告生成器 - 主入口
完全按照原始源代码的交互流程
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.generator import CompleteReportGenerator, AsyncConfig


def get_user_inputs():
    """获取用户输入的路径配置（支持动态配置）"""
    print("🔧 配置报告生成参数")
    print("=" * 50)

    # 1. 获取主题输入路径
    print("\n📝 1. 主题配置")
    topic_input_method = input("选择主题输入方式 (1: 直接输入, 2: 从文件读取) [默认: 1]: ").strip() or "1"

    if topic_input_method == "2":
        topic_file_path = input("请输入主题文件路径 [默认: inputs/topic.txt]: ").strip() or "inputs/topic.txt"
        try:
            with open(topic_file_path, 'r', encoding='utf-8') as f:
                topic = f.read().strip()
            print(f"✅ 从文件读取主题: {topic}")
        except FileNotFoundError:
            print(f"⚠️ 文件不存在: {topic_file_path}，使用默认主题")
            topic = "固态电池产业研究报告"
    else:
        topic = input("请输入报告主题 [默认: 固态电池产业研究报告]: ").strip() or "固态电池产业研究报告"

    # 新增：读取指定框架文件的接口
    print(f"\n📋 报告框架配置")
    print("=" * 50)
    print("您可以提供一个预定义的报告框架文件，系统将严格按照该框架执行")
    print("支持格式：.json, .txt, .md")
    print("如果不提供框架文件，将使用AI自动生成框架")

    framework_file_path = input("请输入框架文件路径（直接回车跳过）: ").strip()
    predefined_framework = None

    if framework_file_path:
        # 临时创建generator实例来加载框架
        temp_generator = CompleteReportGenerator()
        try:
            predefined_framework = temp_generator._load_predefined_framework(framework_file_path)
            if predefined_framework:
                print(f"✅ 成功加载预定义框架: {framework_file_path}")
                print(f"📊 框架包含 {temp_generator._count_framework_nodes(predefined_framework)} 个节点")
            else:
                print(f"❌ 框架文件加载失败，将使用AI自动生成")
                framework_file_path = ""
        except:
            print(f"❌ 框架文件加载失败，将使用AI自动生成")
            framework_file_path = ""
    else:
        print("📝 将使用AI自动生成报告框架")

    # 2. 获取一级标题数量配置
    print("\n🔢 2. 标题层级配置")
    while True:
        try:
            primary_sections = input("请输入一级标题数量 [默认: 8]: ").strip()
            if not primary_sections:
                primary_sections = 8
            else:
                primary_sections = int(primary_sections)
            if primary_sections < 1 or primary_sections > 20:
                print("❌ 一级标题数量应在1-20之间")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")

    # 3. 获取最大层级深度配置
    while True:
        try:
            max_depth = input("请输入最大层级深度 [默认: 6]: ").strip()
            if not max_depth:
                max_depth = 6
            else:
                max_depth = int(max_depth)
            if max_depth < 2 or max_depth > 8:
                print("❌ 最大层级深度应在2-8之间")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")

    print(f"✅ 配置完成: {primary_sections}个一级标题，最大{max_depth}级深度")

    # 4. 获取报告框架读取路径
    print("\n📋 4. 报告框架配置")
    use_custom_framework = input("是否使用自定义框架文件? (y/n) [默认: n]: ").strip().lower() or "n"

    if use_custom_framework == "y":
        framework_path = input("请输入框架文件路径 [默认: templates/custom_framework.md]: ").strip() or "templates/custom_framework.md"
        if not Path(framework_path).exists():
            print(f"⚠️ 框架文件不存在: {framework_path}，将创建默认框架")
            framework_path = ""
    else:
        framework_path = ""
        print(f"✅ 使用AI自动生成框架")

    # 5. 获取每个标题的文档路径配置
    print(f"\n📁 5. 数据源配置")
    use_custom_data_sources = input("是否使用自定义数据源路径? (y/n) [默认: n]: ").strip().lower() or "n"

    if use_custom_data_sources == "y":
        print(f"请输入{primary_sections}个数据源文件夹路径（对应{primary_sections}个一级标题）:")
        data_sources = []

        # 生成默认数据源路径
        default_source_names = [
            "market_overview", "technology_trends", "competitive_landscape",
            "regulatory_environment", "investment_analysis", "risk_assessment",
            "future_outlook", "recommendations"
        ]

        for i in range(primary_sections):
            default_path = f"data/{i+1}_{default_source_names[i % len(default_source_names)]}"
            source_path = input(f"第{i+1}个数据源路径 [默认: {default_path}]: ").strip() or default_path
            data_sources.append(source_path)
    else:
        # 使用默认数据源路径
        default_source_names = [
            "market_overview", "technology_trends", "competitive_landscape",
            "regulatory_environment", "investment_analysis", "risk_assessment",
            "future_outlook", "recommendations"
        ]
        data_sources = [f"data/{i+1}_{default_source_names[i % len(default_source_names)]}" for i in range(primary_sections)]

    # 6. 获取最终报告字数控制
    print(f"\n📊 6. 最终报告字数控制")
    print(f"   注意：这是最终输出报告的字数，中间处理可以使用更多数据")
    while True:
        try:
            target_words = input("请输入最终报告目标字数 [默认: 50000]: ").strip()
            if not target_words:
                target_words = 50000
            else:
                target_words = int(target_words)
            if target_words < 1000:
                print("❌ 目标字数不能少于1000字")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")

    print(f"✅ 最终报告目标字数: {target_words:,} 字")

    # 7. Token限制配置
    print(f"\n🔢 7. Token限制配置")
    print(f"   注意：Token限制用于控制单次API调用的最大输入长度")
    print(f"   超过限制时将自动分批处理，确保API调用成功")
    max_tokens = input("请输入Token限制 [默认: 250000]: ").strip()
    if not max_tokens:
        max_tokens = 250000
    else:
        try:
            max_tokens = int(max_tokens)
        except ValueError:
            max_tokens = 250000

    print(f"✅ Token限制设置: {max_tokens:,} tokens")
    print(f"   💡 分批处理示例:")
    print(f"      {int(max_tokens * 1.32):,} tokens → 2 次调用")
    print(f"      {int(max_tokens * 2.24):,} tokens → 3 次调用")

    # 8. 参考报告配置
    print(f"\n📚 8. 报告参考配置")
    print(f"   注意：参考报告将用于学习写作风格和结构，提升生成报告的质量")
    use_reference = input("是否配置参考报告文件夹? (y/n) [默认: n]: ").strip().lower() or "n"

    reference_reports_path = None
    if use_reference == "y":
        reference_reports_path = input("请输入参考报告文件夹路径 [默认: reference_reports]: ").strip() or "reference_reports"
        if Path(reference_reports_path).exists():
            ref_files = list(Path(reference_reports_path).glob("*.pdf")) + list(Path(reference_reports_path).glob("*.docx")) + list(Path(reference_reports_path).glob("*.txt"))
            print(f"✅ 参考报告文件夹存在，包含 {len(ref_files)} 个文件")
        else:
            print(f"⚠️ 参考报告文件夹不存在: {reference_reports_path}")
            reference_reports_path = None

    return topic, framework_path, data_sources, primary_sections, max_depth, target_words, reference_reports_path, predefined_framework, max_tokens


def main():
    """主函数"""
    print("🤖 完整版AI报告生成器")
    print("=" * 60)
    print("✅ 严格按照用户需求实现：")
    print("   1. 统筹模型(gemini-2.5-pro)读取框架文件并生成报告框架")
    print("   2. 执行模型(gemini-2.5-flash)按框架生成具体内容")
    print("   3. 严谨的3轮迭代优化流程：")
    print("      • 每轮先保存当前版本")
    print("      • 统筹模型审核8个一级标题及下属内容，并进行优化")
    print("      • 统筹模型审核整体文档，并进行优化")
    print("      • 保存优化后版本")
    print("   4. 使用您提供的API轮换机制")
    print("   5. 支持完整的6级标题结构")
    print("   6. 动态配置数据源对应一级章节")
    print("   7. 支持自定义主题、框架和数据源路径")
    print("   8. 🚀 新增：异步并行优化，大幅提升生成速度")

    # 显示异步配置信息
    perf_info = AsyncConfig.get_performance_info()
    print(f"\n⚡ 异步并行配置:")
    print(f"   可用API密钥: {perf_info['available_api_keys']} 个")
    print(f"   最大并发数: {perf_info['max_concurrent_requests']}")
    print(f"   预计总调用: {perf_info['estimated_total_api_calls']} 次")
    print(f"   预计加速: {perf_info['estimated_speedup']}")
    print(f"   输出结果: 与同步版本完全一致，仅提升速度")
    print("=" * 60)

    try:
        # 获取用户输入的配置（包含动态配置、字数控制和Token限制）
        topic, framework_path, data_sources, primary_sections, max_depth, target_words, reference_reports_path, predefined_framework, max_tokens = get_user_inputs()

        print("\n✅ 环境准备完成")

        print(f"\n📊 最终报告参数:")
        print(f"   主题: {topic}")
        print(f"   框架文件: {framework_path}")
        print(f"   一级标题数量: {primary_sections}")
        print(f"   最大层级深度: {max_depth}")
        print(f"   目标字数: {target_words:,} 字")
        print(f"   数据源数量: {len(data_sources)} 个文件夹")
        print(f"   数据源路径:")
        for i, source in enumerate(data_sources, 1):
            print(f"     {i}. {source}")
        print(f"   参考报告路径: {reference_reports_path if reference_reports_path else '未配置'}")

        # 选择运行模式
        print(f"\n🔧 选择运行模式:")
        print(f"   1. 异步并行模式（推荐）- 速度快，结果相同")
        print(f"   2. 同步顺序模式 - 传统模式，稳定可靠")

        mode_choice = input(f"请选择模式 (1/2) [默认: 1]: ").strip() or "1"
        use_async = mode_choice == "1"

        if use_async:
            print(f"✅ 选择异步并行模式")
            print(f"   预计加速: {perf_info['estimated_speedup']}")
            print(f"   输出结果: 与同步模式完全一致")
        else:
            print(f"✅ 选择同步顺序模式")
            print(f"   运行方式: 传统顺序执行")

        # 确认生成
        confirm = input(f"\n🚀 确认开始生成报告? (y/n) [默认: y]: ").strip().lower() or "y"
        if confirm != "y":
            print("❌ 用户取消生成")
            return

        # 生成报告
        generator = CompleteReportGenerator(use_async=use_async, max_tokens=max_tokens)

        # 配置动态参数
        generator.report_config.update({
            "title": topic,
            "primary_sections": primary_sections,
            "max_depth": max_depth,
            "target_words": target_words,
            "reference_report": reference_reports_path,  # 添加参考报告路径
            "predefined_framework": predefined_framework,  # 添加预定义框架
            "data_source": data_sources[0] if data_sources else "",
            "max_tokens": max_tokens  # 添加Token限制
        })

        output_path = generator.generate_report(
            topic=topic,
            data_sources=data_sources,
            framework_file_path=framework_path,
            resume_checkpoint=None  # 正常流程不使用checkpoint恢复
        )

        print(f"\n🎉 报告生成成功!")
        print(f"📄 输出文件: {output_path}")
        print(f"📂 文件位置: {Path(output_path).absolute()}")

        print(f"\n📋 生成特点:")
        print("✅ 严格使用统筹模型(gemini-2.5-pro)进行框架设计")
        print("✅ 严格使用执行模型(gemini-2.5-flash)进行内容生成")
        print("✅ 完整的3轮迭代优化流程")
        print("✅ 支持异步并行处理，大幅提升速度")

    except KeyboardInterrupt:
        print("\n❌ 用户中断生成")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
